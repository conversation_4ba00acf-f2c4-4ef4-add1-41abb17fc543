<template>
  <layout>
    <uv-navbar
      :fixed="false"
      :title="title"
      left-arrow
      @leftClick="$onClickLeft"
    />
    <view class="container">
      <!-- 门店信息 -->
      <view class="shop-info" v-if="store.name">
        <image :src="store.image || store.logo" class="shop-logo"></image>
        <text class="shop-name">{{ store.name }}</text>
        <text class="shop-address">{{ store.address }}</text>
      </view>

      <!-- 有排队信息时显示 -->
      <view class="queue-info" v-if="queueInfo.id">
        <view class="queue-header">
          <text class="queue-title">排队信息</text>
          <view class="queue-number">{{ queueInfo.queueNumber }}</view>
        </view>

        <view class="info-list">
          <view class="info-item">
            <text class="label">排队号码:</text>
            <text class="value highlight">{{ queueInfo.queueNumber }}</text>
          </view>
          <view class="info-item">
            <text class="label">前方等待:</text>
            <text class="value">{{ queueInfo.waitCount || 0 }}桌</text>
          </view>
          <view class="info-item">
            <text class="label">就餐人数:</text>
            <text class="value">{{ queueInfo.peopleCount }}人</text>
          </view>
          <view class="info-item">
            <text class="label">取号时间:</text>
            <text class="value">{{ queueInfo.createTime }}</text>
          </view>
          <view class="info-item">
            <text class="label">状态:</text>
            <text class="value" :class="getStatusClass(queueInfo.status)">
              {{ queueInfo.statusDesc || getStatusText(queueInfo.status) }}
            </text>
          </view>
          <view class="info-item" v-if="queueInfo.remark">
            <text class="label">备注:</text>
            <text class="value">{{ queueInfo.remark }}</text>
          </view>
        </view>

        <view class="action-buttons">
          <button class="cancel-btn" @tap="cancelQueue">取消排队</button>
          <button class="refresh-btn" @tap="refreshQueueInfo">刷新状态</button>
        </view>
      </view>

      <!-- 没有排队信息时显示 -->
      <view class="no-queue" v-else>
        <view class="empty-icon">🎫</view>
        <text class="empty-text">当前没有排队信息</text>
        <text class="empty-desc">请输入就餐人数进行取号</text>

        <view class="input-section">
          <view class="input-group">
            <text class="input-label">就餐人数:</text>
            <uv-input
              v-model="peoples"
              type="number"
              placeholder="请输入就餐人数"
              border="bottom"
              class="people-input"
            ></uv-input>
          </view>
          <view class="input-group" v-if="showCateSelect">
            <text class="input-label">桌面类型:</text>
            <picker
              @change="onCateChange"
              :value="selectedCateIndex"
              :range="cateOptions"
              range-key="name"
            >
              <view class="picker-view">
                {{ selectedCate.name || "请选择桌面类型" }}
              </view>
            </picker>
          </view>
          <view class="input-group">
            <text class="input-label">备注:</text>
            <uv-input
              v-model="remark"
              placeholder="请输入备注信息（可选）"
              border="bottom"
              class="remark-input"
            ></uv-input>
          </view>
        </view>

        <button
          class="take-number-btn"
          @tap="takeNumber"
          :disabled="!peoples || peoples <= 0"
        >
          立即取号
        </button>
      </view>

      <!-- 排队统计信息 -->
      <view class="queue-stats" v-if="queueStats.total > 0">
        <view class="stats-title">排队统计</view>
        <view class="stats-list">
          <view class="stats-item">
            <text class="stats-label">总排队数</text>
            <text class="stats-value">{{ queueStats.total }}</text>
          </view>
          <view
            class="stats-item"
            v-for="item in queueStats.cateQueueCounts"
            :key="item.cateId"
          >
            <text class="stats-label">{{ item.cateName }}</text>
            <text class="stats-value">{{ item.queueCount }}桌</text>
          </view>
        </view>
      </view>
    </view>
  </layout>
</template>

<script setup>
import { ref, computed } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";
import { useMainStore } from "@/store/store";
import { storeToRefs } from "pinia";
import {
  takeNumber as apiTakeNumber,
  cancelQueue as apiCancelQueue,
  getCurrentQueue as apiGetCurrentQueue,
  getShopQueueList as apiGetShopQueueList,
} from "@/api/desk";
import { formatDateTime } from "@/utils/util";

const main = useMainStore();
const { member, store } = storeToRefs(main);

const title = ref("排队叫号");
const queueInfo = ref({});
const peoples = ref(1); // 就餐人数
const remark = ref(""); // 备注信息
const queueStats = ref({}); // 排队统计信息
const cateOptions = ref([]); // 桌面类型选项
const selectedCateIndex = ref(0);
const selectedCate = ref({});

// 计算属性
const showCateSelect = computed(() => {
  return cateOptions.value && cateOptions.value.length > 0;
});

onLoad((options) => {
  // 可以从路由参数获取门店ID和门店名称
  if (options.shopId) {
    store.value.id = options.shopId;
  }
  if (options.shopName) {
    store.value.name = decodeURIComponent(options.shopName);
  }
});

onShow(() => {
  const shopId = store.value.id || member.value.shopId || 1;
  if (shopId) {
    getCurrentQueue(shopId);
    getQueueStats(shopId);
  }
});

// 获取当前排队信息
const getCurrentQueue = async (shopId) => {
  try {
    const data = await apiGetCurrentQueue({ shopId });
    if (data) {
      queueInfo.value = {
        ...data,
        createTime: formatDateTime(data.createTime),
      };
    } else {
      queueInfo.value = {};
    }
  } catch (error) {
    console.error("获取当前排队信息失败:", error);
    uni.showToast({
      title: "获取排队信息失败",
      icon: "none",
    });
  }
};

// 获取排队统计信息
const getQueueStats = async (shopId) => {
  try {
    const data = await apiGetShopQueueList({ shopId });
    if (data) {
      queueStats.value = data;
      // 如果有桌面类型信息，设置选项
      if (data.cateQueueCounts && data.cateQueueCounts.length > 0) {
        cateOptions.value = data.cateQueueCounts.map((item) => ({
          id: item.cateId,
          name: item.cateName,
        }));
        selectedCate.value = cateOptions.value[0] || {};
      }
    }
  } catch (error) {
    console.error("获取排队统计失败:", error);
  }
};

// 桌面类型选择
const onCateChange = (e) => {
  selectedCateIndex.value = e.detail.value;
  selectedCate.value = cateOptions.value[e.detail.value] || {};
};

// 取号
const takeNumber = async () => {
  const shopId = store.value.id || member.value.shopId || 1;

  if (!shopId) {
    uni.showToast({
      title: "请选择门店",
      icon: "none",
    });
    return;
  }

  if (!peoples.value || peoples.value <= 0) {
    uni.showToast({
      title: "请输入正确的就餐人数",
      icon: "none",
    });
    return;
  }

  try {
    const params = {
      shopId,
      peopleCount: peoples.value,
      remark: remark.value,
    };

    // 如果选择了桌面类型
    if (selectedCate.value.id) {
      params.cateId = selectedCate.value.id;
    }

    const data = await apiTakeNumber(params);
    if (data) {
      queueInfo.value = {
        ...data,
        createTime: formatDateTime(data.createTime),
      };
      uni.showToast({
        title: "取号成功",
        icon: "success",
      });
      // 重新获取统计信息
      getQueueStats(shopId);
    } else {
      uni.showToast({
        title: "取号失败",
        icon: "none",
      });
    }
  } catch (error) {
    console.error("取号失败:", error);
    uni.showToast({
      title: "取号失败",
      icon: "none",
    });
  }
};

// 取消排队
const cancelQueue = async () => {
  if (!queueInfo.value.id) {
    uni.showToast({
      title: "没有可取消的排队信息",
      icon: "none",
    });
    return;
  }

  uni.showModal({
    title: "确认取消",
    content: "确定要取消排队吗？",
    success: async (res) => {
      if (res.confirm) {
        try {
          await apiCancelQueue({ id: queueInfo.value.id });
          queueInfo.value = {};
          uni.showToast({
            title: "取消排队成功",
            icon: "success",
          });
          // 重新获取统计信息
          const shopId = store.value.id || member.value.shopId || 1;
          getQueueStats(shopId);
        } catch (error) {
          console.error("取消排队失败:", error);
          uni.showToast({
            title: "取消排队失败",
            icon: "none",
          });
        }
      }
    },
  });
};

// 刷新排队信息
const refreshQueueInfo = async () => {
  const shopId = store.value.id || member.value.shopId || 1;
  if (shopId) {
    Promise.all(getCurrentQueue(shopId), getQueueStats(shopId)).then(() => {
      uni.showToast({
        title: "刷新成功",
        icon: "success",
      });
    });
  }
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    0: "排队中",
    1: "已叫号",
    2: "已取消",
    3: "已过期",
  };
  return statusMap[status] || "未知状态";
};

// 获取状态样式类
const getStatusClass = (status) => {
  const classMap = {
    0: "status-waiting",
    1: "status-called",
    2: "status-cancelled",
    3: "status-expired",
  };
  return classMap[status] || "";
};
</script>

<style lang="scss" scoped>
.container {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

// 门店信息
.shop-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

  .shop-logo {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    margin-bottom: 20rpx;
  }

  .shop-name {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
  }

  .shop-address {
    font-size: 28rpx;
    color: #666;
  }
}

// 排队信息卡片
.queue-info {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

  .queue-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;
    padding-bottom: 20rpx;
    border-bottom: 1rpx solid #eee;

    .queue-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .queue-number {
      background: linear-gradient(135deg, #f9ae3d, #ff6b35);
      color: #fff;
      padding: 10rpx 20rpx;
      border-radius: 20rpx;
      font-size: 28rpx;
      font-weight: bold;
    }
  }

  .info-list {
    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .label {
        color: #666;
        font-size: 28rpx;
      }

      .value {
        color: #333;
        font-size: 28rpx;
        font-weight: 500;

        &.highlight {
          color: #f9ae3d;
          font-weight: bold;
          font-size: 32rpx;
        }

        &.status-waiting {
          color: #f9ae3d;
        }

        &.status-called {
          color: #52c41a;
        }

        &.status-cancelled {
          color: #ff4d4f;
        }

        &.status-expired {
          color: #999;
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 20rpx;
    margin-top: 40rpx;

    button {
      flex: 1;
      height: 80rpx;
      border-radius: 40rpx;
      font-size: 28rpx;
      border: none;

      &.cancel-btn {
        background-color: #ff4d4f;
        color: #fff;
      }

      &.refresh-btn {
        background-color: #1890ff;
        color: #fff;
      }
    }
  }
}

// 无排队信息状态
.no-queue {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 60rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

  .empty-icon {
    font-size: 80rpx;
    margin-bottom: 20rpx;
  }

  .empty-text {
    color: #333;
    font-size: 32rpx;
    font-weight: 500;
    margin-bottom: 10rpx;
  }

  .empty-desc {
    color: #999;
    font-size: 28rpx;
    margin-bottom: 40rpx;
  }

  .input-section {
    width: 100%;
    margin-bottom: 40rpx;

    .input-group {
      display: flex;
      align-items: center;
      margin-bottom: 30rpx;

      .input-label {
        font-size: 28rpx;
        color: #333;
        margin-right: 20rpx;
        min-width: 140rpx;
      }

      .people-input,
      .remark-input {
        flex: 1;
      }

      .picker-view {
        flex: 1;
        padding: 20rpx 0;
        color: #333;
        font-size: 28rpx;
        border-bottom: 1rpx solid #eee;
      }
    }
  }

  .take-number-btn {
    background: linear-gradient(135deg, #f9ae3d, #ff6b35);
    color: #fff;
    border-radius: 40rpx;
    width: 80%;
    height: 80rpx;
    font-size: 30rpx;
    font-weight: bold;
    border: none;

    &[disabled] {
      background: #ccc;
      color: #999;
    }
  }
}

// 排队统计
.queue-stats {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

  .stats-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
    padding-bottom: 15rpx;
    border-bottom: 1rpx solid #eee;
  }

  .stats-list {
    .stats-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15rpx 0;

      .stats-label {
        color: #666;
        font-size: 28rpx;
      }

      .stats-value {
        color: #f9ae3d;
        font-size: 28rpx;
        font-weight: bold;
      }
    }
  }
}
</style>
