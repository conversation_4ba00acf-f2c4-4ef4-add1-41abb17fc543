<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-form-item label="门店" prop="shopId">
        <el-select
          v-model="formData.shopId"
          placeholder="请选择门店"
          class="w-full"
        >
          <el-option
            v-for="shop in shopOptions"
            :key="shop.id"
            :label="shop.name"
            :value="shop.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="桌面分类" prop="cateId">
        <el-select
          v-model="formData.cateId"
          placeholder="请选择桌面分类"
          class="w-full"
        >
          <el-option
            v-for="cate in cateOptions"
            :key="cate.id"
            :label="cate.name"
            :value="cate.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="用户名称" prop="nickname">
        <el-input v-model="formData.nickname" placeholder="请输入用户名称" />
      </el-form-item>
      <el-form-item label="手机号" prop="mobile">
        <el-input v-model="formData.mobile" placeholder="请输入手机号" />
      </el-form-item>
      <el-form-item label="就餐人数" prop="peopleCount">
        <el-input-number
          v-model="formData.peopleCount"
          :min="1"
          :max="20"
          class="w-full"
        />
      </el-form-item>
      <el-form-item label="预约时间" prop="appointmentTime">
        <el-date-picker
          v-model="formData.appointmentTime"
          type="datetime"
          placeholder="选择预约时间"
          value-format="YYYY-MM-DD HH:mm:ss"
          class="w-full"
        />
      </el-form-item>
      <el-form-item label="排队状态" prop="status">
        <el-select
          v-model="formData.status"
          placeholder="请选择排队状态"
          class="w-full"
        >
          <el-option
            v-for="dict in statusOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          placeholder="请输入备注信息"
          :rows="4"
        />
      </el-form-item>
      
      <!-- 高级设置 -->
      <el-divider content-position="center">高级设置</el-divider>
      
      <el-form-item label="优先级" prop="priority">
        <el-select
          v-model="advancedSettings.priority"
          placeholder="请选择优先级"
          class="w-full"
        >
          <el-option label="普通" :value="0" />
          <el-option label="优先" :value="1" />
          <el-option label="VIP" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="预估等待时间" prop="estimatedWaitTime">
        <el-input-number
          v-model="advancedSettings.estimatedWaitTime"
          :min="0"
          :max="180"
          :step="5"
          class="w-full"
        />
        <div class="text-gray-400 text-sm mt-1">单位：分钟（系统会根据当前排队情况自动计算）</div>
      </el-form-item>
      <el-form-item label="通知方式" prop="notifyMethod">
        <el-checkbox-group v-model="advancedSettings.notifyMethods">
          <el-checkbox label="sms">短信</el-checkbox>
          <el-checkbox label="wechat">微信</el-checkbox>
          <el-checkbox label="app">APP推送</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import * as ShopDeskQueueApi from '@/api/mall/desk/mqorder'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改

// 门店选项
const shopOptions = ref([
  { id: 1, name: '总店' },
  { id: 2, name: '北京分店' },
  { id: 3, name: '上海分店' }
])

// 桌面分类选项
const cateOptions = ref([
  { id: 1, name: '大桌' },
  { id: 2, name: '中桌' },
  { id: 3, name: '小桌' },
  { id: 4, name: 'VIP包间' }
])

// 排队状态选项
const statusOptions = [
  { label: '排队中', value: 1 },
  { label: '已叫号', value: 2 },
  { label: '已取消', value: 3 },
  { label: '已过号', value: 4 },
  { label: '已入座', value: 5 }
]

// 高级设置
const advancedSettings = reactive({
  priority: 0,
  estimatedWaitTime: 15,
  notifyMethods: ['wechat']
})

const formData = ref({
  id: undefined,
  shopId: undefined,
  cateId: undefined,
  uid: undefined,
  nickname: undefined,
  mobile: undefined,
  peopleCount: 1,
  status: 1,
  remark: undefined,
  appointmentTime: undefined
})

const formRules = reactive({
  shopId: [{ required: true, message: '门店不能为空', trigger: 'change' }],
  cateId: [{ required: true, message: '桌面分类不能为空', trigger: 'change' }],
  nickname: [{ required: true, message: '用户名称不能为空', trigger: 'blur' }],
  mobile: [
    { required: true, message: '手机号不能为空', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  peopleCount: [{ required: true, message: '就餐人数不能为空', trigger: 'blur' }]
})

const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const res = await ShopDeskQueueApi.getShopDeskQueue(id)
      formData.value = res.data
      
      // 设置高级选项的默认值
      // 实际项目中，这些值应该从后端获取
      advancedSettings.priority = 0
      advancedSettings.estimatedWaitTime = 15
      advancedSettings.notifyMethods = ['wechat']
    } catch (error) {
      console.error('获取排队叫号详情失败:', error)
      message.error('获取排队叫号详情失败')
    } finally {
      formLoading.value = false
    }
  }
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  
  // 提交请求
  formLoading.value = true
  try {
    const data = { ...formData.value } as unknown as ShopDeskQueueApi.ShopDeskQueueCreateReqVO | ShopDeskQueueApi.ShopDeskQueueUpdateReqVO
    
    if (formType.value === 'create') {
      await ShopDeskQueueApi.createShopDeskQueue(data as ShopDeskQueueApi.ShopDeskQueueCreateReqVO)
      message.success(t('common.createSuccess'))
    } else {
      await ShopDeskQueueApi.updateShopDeskQueue(data as ShopDeskQueueApi.ShopDeskQueueUpdateReqVO)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } catch (error) {
    console.error('保存排队叫号失败:', error)
    message.error('保存排队叫号失败')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    shopId: undefined,
    cateId: undefined,
    uid: undefined,
    nickname: undefined,
    mobile: undefined,
    peopleCount: 1,
    status: 1,
    remark: undefined,
    appointmentTime: undefined
  }
  
  // 重置高级设置
  advancedSettings.priority = 0
  advancedSettings.estimatedWaitTime = 15
  advancedSettings.notifyMethods = ['wechat']
  
  formRef.value?.resetFields()
}
</script>