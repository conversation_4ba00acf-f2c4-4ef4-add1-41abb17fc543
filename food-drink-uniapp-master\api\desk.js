import api from './api'

/**
 * Food点餐系统 - 门店排队叫号 API
 * 基于OpenAPI 3.0.1规范生成
 * 版本: 3.1.2
 */

/**
 * 获取当前排队信息
 * @param {object} data - 请求参数
 * @param {number} data.shopId - 门店ID (必填)
 * @returns {Promise} 当前排队信息
 */
export function getCurrentQueue(data) {
  return api.get(`/queue/current`, data, { login: true })
}

/**
 * 取号 - 创建新的排队记录
 * @param {object} data - 排队数据
 * @param {number} data.shopId - 门店ID (必填)
 * @param {number} data.cateId - 桌面分类ID (必填)
 * @param {number} data.peopleCount - 就餐人数 (必填)
 * @param {string} data.remark - 备注 (可选)
 * @param {number} data.uid - 用户ID (可选)
 * @returns {Promise} 创建的排队信息
 */
export function takeNumber(data) {
  return api.post(`/queue/take-number`, data, { login: true })
}

/**
 * 取消排队
 * @param {object} data - 请求参数
 * @param {number} data.id - 排队ID (必填)
 * @returns {Promise} 取消结果
 */
export function cancelQueue(data) {
  return api.post(`/queue/cancel`, data, { login: true })
}

/**
 * 获取门店排队列表
 * @param {object} data - 请求参数
 * @param {number} data.shopId - 门店ID (必填)
 * @returns {Promise} 门店排队列表信息
 */
export function getShopQueueList(data) {
  return api.get(`/queue/list`, data, { login: true })
}