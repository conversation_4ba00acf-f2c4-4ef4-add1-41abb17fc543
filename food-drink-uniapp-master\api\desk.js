/**
 * Food点餐系统 - 门店排队叫号 API 客户端
 * 基于OpenAPI 3.0.1规范生成
 * 服务器地址: http://localhost:48081
 * 版本: 3.1.2
 */

// 基础配置
const API_CONFIG = {
  baseURL: 'http://localhost:48081',
  defaultHeaders: {
    'Content-Type': 'application/json',
    'tenant-id': '1',
    'Authorization': 'Bearer test1'
  }
};

/**
 * HTTP请求工具函数
 * @param {string} url - 请求URL
 * @param {object} options - 请求选项
 * @returns {Promise} 请求结果
 */
async function request(url, options = {}) {
  const config = {
    method: 'GET',
    headers: { ...API_CONFIG.defaultHeaders },
    ...options
  };

  // 合并自定义headers
  if (options.headers) {
    config.headers = { ...config.headers, ...options.headers };
  }

  try {
    const response = await fetch(`${API_CONFIG.baseURL}${url}`, config);
    const result = await response.json();
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${result.msg || '请求失败'}`);
    }
    
    return result;
  } catch (error) {
    console.error('API请求错误:', error);
    throw error;
  }
}

/**
 * 门店排队叫号 API 类
 */
class QueueAPI {
  
  /**
   * 获取当前排队信息
   * @param {number} shopId - 门店ID (必填)
   * @param {object} options - 可选参数
   * @param {number} options.tenantId - 租户编号 (默认: 1)
   * @param {string} options.authorization - 认证Token (默认: "Bearer test1")
   * @returns {Promise<object>} 当前排队信息
   */
  static async getCurrentQueue(shopId, options = {}) {
    if (!shopId) {
      throw new Error('shopId 参数是必填的');
    }

    const headers = {};
    if (options.tenantId) headers['tenant-id'] = options.tenantId;
    if (options.authorization) headers['Authorization'] = options.authorization;

    const url = `/app-api/queue/current?shopId=${shopId}`;
    return await request(url, { headers });
  }

  /**
   * 取号 - 创建新的排队记录
   * @param {object} queueData - 排队数据
   * @param {number} queueData.shopId - 门店ID (必填)
   * @param {number} queueData.cateId - 桌面分类ID (必填)
   * @param {number} queueData.peopleCount - 就餐人数 (必填)
   * @param {string} queueData.remark - 备注 (可选)
   * @param {number} queueData.uid - 用户ID (可选)
   * @param {object} options - 可选参数
   * @param {number} options.tenantId - 租户编号 (默认: 1)
   * @param {string} options.authorization - 认证Token (默认: "Bearer test1")
   * @returns {Promise<object>} 创建的排队信息
   */
  static async takeNumber(queueData, options = {}) {
    const { shopId, cateId, peopleCount, remark, uid } = queueData;
    
    // 验证必填参数
    if (!shopId || !cateId || !peopleCount) {
      throw new Error('shopId, cateId, peopleCount 参数是必填的');
    }

    const headers = {};
    if (options.tenantId) headers['tenant-id'] = options.tenantId;
    if (options.authorization) headers['Authorization'] = options.authorization;

    const body = {
      shopId,
      cateId,
      peopleCount,
      ...(remark && { remark }),
      ...(uid && { uid })
    };

    return await request('/app-api/queue/take-number', {
      method: 'POST',
      headers,
      body: JSON.stringify(body)
    });
  }

  /**
   * 取消排队
   * @param {number} id - 排队ID (必填)
   * @param {object} options - 可选参数
   * @param {number} options.tenantId - 租户编号 (默认: 1)
   * @param {string} options.authorization - 认证Token (默认: "Bearer test1")
   * @returns {Promise<object>} 取消结果
   */
  static async cancelQueue(id, options = {}) {
    if (!id) {
      throw new Error('id 参数是必填的');
    }

    const headers = {};
    if (options.tenantId) headers['tenant-id'] = options.tenantId;
    if (options.authorization) headers['Authorization'] = options.authorization;

    const url = `/app-api/queue/cancel?id=${id}`;
    return await request(url, {
      method: 'POST',
      headers
    });
  }

  /**
   * 获取门店排队列表
   * @param {number} shopId - 门店ID (必填)
   * @param {object} options - 可选参数
   * @param {number} options.tenantId - 租户编号 (默认: 1)
   * @param {string} options.authorization - 认证Token (默认: "Bearer test1")
   * @returns {Promise<object>} 门店排队列表信息
   */
  static async getShopQueueList(shopId, options = {}) {
    if (!shopId) {
      throw new Error('shopId 参数是必填的');
    }

    const headers = {};
    if (options.tenantId) headers['tenant-id'] = options.tenantId;
    if (options.authorization) headers['Authorization'] = options.authorization;

    const url = `/app-api/queue/list?shopId=${shopId}`;
    return await request(url, { headers });
  }
}

/**
 * 便捷的API调用函数
 */
const DeskAPI = {
  // 获取当前排队信息
  getCurrentQueue: QueueAPI.getCurrentQueue,
  
  // 取号
  takeNumber: QueueAPI.takeNumber,
  
  // 取消排队
  cancelQueue: QueueAPI.cancelQueue,
  
  // 获取门店排队列表
  getShopQueueList: QueueAPI.getShopQueueList,

  /**
   * 设置全局配置
   * @param {object} config - 配置对象
   * @param {string} config.baseURL - API基础URL
   * @param {string} config.authorization - 认证Token
   * @param {number} config.tenantId - 租户ID
   */
  setConfig(config) {
    if (config.baseURL) {
      API_CONFIG.baseURL = config.baseURL;
    }
    if (config.authorization) {
      API_CONFIG.defaultHeaders.Authorization = config.authorization;
    }
    if (config.tenantId) {
      API_CONFIG.defaultHeaders['tenant-id'] = config.tenantId;
    }
  }
};

/**
 * 使用示例
 */
const Examples = {

  // 示例1: 获取当前排队信息
  async getCurrentQueueExample() {
    try {
      const result = await DeskAPI.getCurrentQueue(1001);
      console.log('当前排队信息:', result);
      /*
      返回数据结构:
      {
        code: 200,
        msg: "success",
        data: {
          id: 123,
          shopId: 1001,
          shopName: "测试门店",
          cateId: 1,
          cateName: "大桌",
          queueNumber: "A001",
          peopleCount: 4,
          status: 1,
          statusDesc: "等待中",
          callTime: null,
          seatTime: null,
          remark: "靠窗",
          waitCount: 5,
          createTime: "2024-01-01T10:00:00"
        }
      }
      */
    } catch (error) {
      console.error('获取排队信息失败:', error);
    }
  },

  // 示例2: 取号
  async takeNumberExample() {
    try {
      const queueData = {
        shopId: 1001,
        cateId: 1,
        peopleCount: 4,
        remark: '靠窗位置',
        uid: 12345
      };

      const result = await DeskAPI.takeNumber(queueData);
      console.log('取号成功:', result);
    } catch (error) {
      console.error('取号失败:', error);
    }
  },

  // 示例3: 取消排队
  async cancelQueueExample() {
    try {
      const result = await DeskAPI.cancelQueue(123);
      console.log('取消排队结果:', result);
      /*
      返回数据结构:
      {
        code: 200,
        msg: "success",
        data: true
      }
      */
    } catch (error) {
      console.error('取消排队失败:', error);
    }
  },

  // 示例4: 获取门店排队列表
  async getShopQueueListExample() {
    try {
      const result = await DeskAPI.getShopQueueList(1001);
      console.log('门店排队列表:', result);
      /*
      返回数据结构:
      {
        code: 200,
        msg: "success",
        data: {
          currentQueue: { ... }, // 当前用户的排队信息
          cateQueueCounts: [     // 分类排队统计
            {
              cateId: 1,
              cateName: "大桌",
              queueCount: 10
            }
          ],
          queueList: [ ... ],    // 排队列表
          total: 25
        }
      }
      */
    } catch (error) {
      console.error('获取排队列表失败:', error);
    }
  },

  // 示例5: 自定义配置
  configExample() {
    // 设置自定义配置
    DeskAPI.setConfig({
      baseURL: 'https://your-api-domain.com',
      authorization: 'Bearer your-token-here',
      tenantId: 2
    });
  }
};

/**
 * 错误码说明
 */
const ErrorCodes = {
  200: '成功',
  400: '请求参数错误',
  401: '未授权',
  403: '禁止访问',
  404: '资源不存在',
  500: '服务器内部错误'
};

/**
 * 排队状态枚举 (参考 QueueStatusEnum)
 */
const QueueStatus = {
  WAITING: 1,      // 等待中
  CALLED: 2,       // 已叫号
  SEATED: 3,       // 已入座
  CANCELLED: 4,    // 已取消
  EXPIRED: 5       // 已过期
};

// 导出API
if (typeof module !== 'undefined' && module.exports) {
  // Node.js环境
  module.exports = {
    DeskAPI,
    QueueAPI,
    API_CONFIG,
    Examples,
    ErrorCodes,
    QueueStatus
  };
} else {
  // 浏览器环境
  window.DeskAPI = DeskAPI;
  window.QueueAPI = QueueAPI;
  window.Examples = Examples;
  window.ErrorCodes = ErrorCodes;
  window.QueueStatus = QueueStatus;
}
