<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="da4d3ec5-25f1-4fdf-9cb1-0cdf108e62bc" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/food-module-mall/food-module-desk-biz/src/main/java/ddyy123/top/food/module/desk/controller/admin/shopdesk/ShopDeskQueueController.java" beforeDir="false" afterPath="$PROJECT_DIR$/food-module-mall/food-module-desk-biz/src/main/java/ddyy123/top/food/module/desk/controller/admin/shopdesk/ShopDeskQueueController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/food-module-mall/food-module-desk-biz/src/main/java/ddyy123/top/food/module/desk/controller/admin/shopdeskcategory/vo/ShopDeskCategorySaveReqVO.java" beforeDir="false" afterPath="$PROJECT_DIR$/food-module-mall/food-module-desk-biz/src/main/java/ddyy123/top/food/module/desk/controller/admin/shopdeskcategory/vo/ShopDeskCategorySaveReqVO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/food-module-mall/food-module-desk-biz/src/main/java/ddyy123/top/food/module/desk/controller/app/shopdesk/vo/queue/AppShopDeskQueueCreateReqVO.java" beforeDir="false" afterPath="$PROJECT_DIR$/food-module-mall/food-module-desk-biz/src/main/java/ddyy123/top/food/module/desk/controller/app/shopdesk/vo/queue/AppShopDeskQueueCreateReqVO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/food-module-mall/food-module-desk-biz/src/main/java/ddyy123/top/food/module/desk/service/shopdesk/ShopDeskQueueService.java" beforeDir="false" afterPath="$PROJECT_DIR$/food-module-mall/food-module-desk-biz/src/main/java/ddyy123/top/food/module/desk/service/shopdesk/ShopDeskQueueService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/food-module-mall/food-module-desk-biz/src/main/java/ddyy123/top/food/module/desk/service/shopdesk/ShopDeskQueueServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/food-module-mall/food-module-desk-biz/src/main/java/ddyy123/top/food/module/desk/service/shopdesk/ShopDeskQueueServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/food-module-mall/food-module-desk-biz/src/main/java/ddyy123/top/food/module/desk/service/shopdeskcategory/ShopDeskCategoryService.java" beforeDir="false" afterPath="$PROJECT_DIR$/food-module-mall/food-module-desk-biz/src/main/java/ddyy123/top/food/module/desk/service/shopdeskcategory/ShopDeskCategoryService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/food-module-mall/food-module-desk-biz/src/main/java/ddyy123/top/food/module/desk/service/shopdeskcategory/ShopDeskCategoryServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/food-module-mall/food-module-desk-biz/src/main/java/ddyy123/top/food/module/desk/service/shopdeskcategory/ShopDeskCategoryServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/food-server/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/food-server/pom.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MacroExpansionManager">
    <option name="directoryName" value="4yp9lbug" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="userSettingsFile" value="D:\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "customColor": "",
  "associatedIndex": 1
}]]></component>
  <component name="ProjectId" id="2z22iGnfFD72o5Je8mMwyFPkS3f" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.FoodServerApplication.executor": "Run",
    "kotlin-language-version-configured": "true",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "org.rust.first.attach.projects": "true",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="FoodServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="food-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="ddyy123.top.food.server.FoodServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="RustProjectSettings">
    <option name="toolchainHomeDirectory" value="$USER_HOME$/.cargo/bin" />
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-39b83d9b5494-intellij.indexing.shared.core-IU-241.18034.62" />
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-IU-241.18034.62" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="da4d3ec5-25f1-4fdf-9cb1-0cdf108e62bc" name="更改" comment="" />
      <created>1750914972735</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750914972735</updated>
      <workItem from="1750914973946" duration="5418000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>