<template>
  <layout>
    <!-- #ifdef MP-WEIXIN -->
    <uv-navbar
      :fixed="false"
      :title="title"
      left-arrow
      @leftClick="$onClickLeft"
    />
    <!-- #endif -->
    <view class="container">
      <view class="my-background">
        <view style="">
          <view class="flex justify-between p-3 align-center">
            <view class="flex">
              <image
                :src="
                  isLogin
                    ? member.avatar
                      ? member.avatar
                      : '/static/images/mine/default.png'
                    : '/static/images/mine/default.png'
                "
                class="rounded-circle avatar-img"
              ></image>
              <view
                class="flex flex-column text-white justify-center align-cente ml-2"
                v-if="isLogin"
              >
                <text class="nick-text">{{ member.nickname }}</text>
                <text class="id-text">用户id:{{ member.id }}</text>
              </view>
              <view
                class="flex flex-column justify-center align-cente ml-2"
                v-else
                @tap="login"
              >
                <text class="nick-text">登录获取更多会员权益</text>
              </view>
            </view>
            <view
              class="mt-2"
              v-if="isLogin"
              @tap="
                serv({
                  type: 'pages',
                  pages: '/pages/components/pages/mine/userinfo',
                })
              "
            >
              <image src="/static/images/mine/set.png" class="icon-img"></image>
            </view>
            <view
              class="mt-2"
              v-else
              @tap="
                serv({
                  type: 'pages',
                  pages: '/pages/components/pages/login/login',
                })
              "
            >
              <uv-button
                text="授权登录"
                size="small"
                shape="circle"
                color="#09b4f1"
              ></uv-button>
            </view>
          </view>
          <view class="vip-box" v-if="isLogin">
            <view class="vip-box-content">
              <view class="vip-box-left flex justify-center align-center">
                <image
                  src="/static/images/mine/vip.png"
                  class="icon-img"
                ></image>
                <text class="font-size-26 ml-2 text-white">{{
                  member.cardId > 0 ? member.cardName : "未开通会员"
                }}</text>
              </view>
              <view
                class="vip-box-right flex justify-center align-center"
                @tap="goVip"
              >
                <view class="font-size-24">{{
                  member.cardId > 0 ? "查看详情" : "立即激活"
                }}</view
                ><view class="iconfont iconarrow-right line-height-100"></view>
              </view>
            </view>
            <view>
              <image
                src="/static/images/mine/cardbg.png"
                style="
                  position: absolute;
                  top: 10rpx;
                  left: 0;
                  right: 0;
                  margin: auto;
                  width: 700rpx;
                  height: 120rpx;
                "
              ></image>
            </view>
          </view>
        </view>
        <view style="padding: 0 30rpx">
          <!-- user box begin -->
          <view class="d-flex flex-column bg-white user-box">
            <!-- user grid begin -->
            <view class="w-100 d-flex align-items-center just-content-center">
              <view
                class="user-grid"
                @tap="
                  serv({
                    type: 'pages',
                    pages: '/pages/components/pages/coupons/coupons',
                  })
                "
              >
                <view
                  class="value font-size-extra-lg font-weight-bold text-color-base"
                >
                  {{ isLogin ? member.couponCount : 0 }}
                </view>
                <view class="font-size-sm text-color-assist">优惠券</view>
              </view>
              <view
                class="user-grid"
                @tap="
                  serv({
                    type: 'pages',
                    pages: '/pages/components/pages/balance/bill?cate=1',
                  })
                "
              >
                <view
                  class="value font-size-extra-lg font-weight-bold text-color-base"
                >
                  {{ isLogin ? member.integral : 0 }}
                </view>
                <view class="font-size-sm text-color-assist">积分</view>
              </view>
              <view
                class="user-grid"
                @tap="
                  serv({
                    type: 'pages',
                    pages: '/pages/components/pages/balance/balance',
                  })
                "
              >
                <view
                  class="value font-size-extra-lg font-weight-bold text-color-base"
                >
                  {{ isLogin ? member.nowMoney : 0 }}
                </view>
                <view class="font-size-sm text-color-assist">余额</view>
              </view>
              <view
                class="user-grid"
                @tap="
                  serv({
                    type: 'pages',
                    pages: '/pages/components/pages/balance/bill?cate=0',
                  })
                "
              >
                <view
                  class="value font-size-extra-lg font-weight-bold text-color-base"
                >
                  {{ isLogin ? member.sumMoney : 0 }}
                </view>
                <view class="font-size-sm text-color-assist">历史消费</view>
              </view>
            </view>
            <!-- user grid end -->
          </view>
          <!-- user box end -->
        </view>
      </view>

      <view class="flex bg-white p-3 justify-between mx-3 mb-1">
        <view
          class="flex flex-column align-center"
          v-for="item in orderTabs"
          :key="item.index"
          @tap="toDetail(item.path, item.index)"
        >
          <view>
            <image :src="item.image" class="item-image"></image>
          </view>
          <view class="font-size-sm text-color-assist">{{ item.title }}</view>
        </view>
      </view>
      <view class="flex bg-white p-3 justify-between mx-3 mb-1">
        <view
          class="flex flex-column align-center"
          v-for="item in couponTabs"
          :key="item.index"
          @tap="toDetail(item.path, item.index)"
        >
          <view>
            <image :src="item.image" class="item-image"></image>
          </view>
          <view class="font-size-sm text-color-assist">{{ item.title }}</view>
        </view>
      </view>
      <!-- service box begin -->
      <view class="service-box">
        <view
          class="font-size-lg text-color-base font-weight-bold"
          style="margin-bottom: 50rpx"
          >我的服务</view
        >
        <view class="u-m-t-20">
          <uv-grid :border="false" :col="4">
            <block v-for="(item, index) in services" :key="index">
              <uv-grid-item
                v-if="item.type == 'call'"
                v-on:click="makePhoneCall(item.phone)"
              >
                <image
                  :src="item.image"
                  style="width: 40rpx; height: 40rpx"
                ></image>
                <text class="font-size-sm text-color-assist my-2">{{
                  item.name
                }}</text>
              </uv-grid-item>
              <uv-grid-item v-else @tap="serv(item)">
                <image
                  :src="item.image"
                  style="width: 40rpx; height: 40rpx"
                ></image>
                <text class="font-size-sm text-color-assist my-2">{{
                  item.name
                }}</text>
              </uv-grid-item>
            </block>
          </uv-grid>
        </view>
      </view>
    </view>
  </layout>
</template>

<script setup>
import { ref, computed } from "vue";
import { useMainStore } from "@/store/store";
import { storeToRefs } from "pinia";
import { onLoad, onShow } from "@dcloudio/uni-app";
import { formatDateTime, kmUnit } from "@/utils/util";
import { userGetUserInfo, mineService } from "@/api/user";
import { check } from "@/api/merchant";
const main = useMainStore();
const { member, isLogin } = storeToRefs(main);

const title = ref("个人中心");
const services = ref([]);
const orderTabs = [
  {
    title: "未支付",
    index: 1,
    image: "/static/images/mine/no_pay.png",
    path: "/pages/components/pages/orders/orders",
  },
  {
    title: "进行中",
    index: 2,
    image: "/static/images/mine/no_take.png",
    path: "/pages/components/pages/orders/orders",
  },
  {
    title: "已完成",
    index: 3,
    image: "/static/images/mine/no_comment.png",
    path: "/pages/components/pages/orders/orders",
  },
  {
    title: "退款单",
    index: 4,
    image: "/static/images/mine/change_order.png",
    path: "/pages/components/pages/orders/orders",
  },
  {
    title: "全部订单",
    index: 0,
    image: "/static/images/mine/all_order.png",
    path: "/pages/components/pages/orders/orders",
  },
];

const couponTabs = [
  {
    title: "已领取",
    index: 0,
    image: "/static/images/mine/nouse_coupon.png",
    path: "/pages/components/pages/coupons/coupons",
  },
  {
    title: "已使用",
    index: 1,
    image: "/static/images/mine/useend_coupon.png",
    path: "/pages/components/pages/coupons/coupons",
  },
  {
    title: "已失效",
    index: 2,
    image: "/static/images/mine/out_coupon.png",
    path: "/pages/components/pages/coupons/coupons",
  },
  {
    title: "领劵中心",
    index: 4,
    image: "/static/images/mine/all_coupon.png",
    path: "/pages/components/pages/coupons/coupons",
  },
];

const growthValue = computed(() => {
  if (!isLogin.value) return 0;
  const { currentValue, needValue } = member.value;
  return (currentValue / (currentValue + needValue)) * 100;
});

onLoad(() => {
  getServices();
});
onShow(() => {
  getUserInfo();
});

const toDetail = (page, index) => {
  uni.navigateTo({
    url: page + "?current=" + index,
  });
};
const getUserInfo = async () => {
  if (isLogin.value) {
    let data = await userGetUserInfo();
    if (data) {
      main.SET_MEMBER(data);
    }
  }
};
const getServices = async () => {
  let data = await mineService();
  if (data) {
    services.value = data;
  }
};
const makePhoneCall = (phoneNumber) => {
  uni.makePhoneCall({
    phoneNumber: phoneNumber,
  });
};
const login = () => {
  uni.navigateTo({
    url: "/pages/components/pages/login/login",
  });
};
const packages = () => {
  if (!isLogin.value) {
    login();
    return;
  }
  uni.navigateTo({
    url: "/pages/components/pages/packages/index",
  });
};
const serv = async (item) => {
  switch (item.type) {
    case "pages":
      if (item.pages == "no") {
        uni.showToast({
          title: "开发中......",
          icon: "error",
        });
        return;
      }
      if (!isLogin.value) {
        login();
        return;
      }
      if (item.pages == "/pages/components/pages/merchant/index") {
        let res = await check();
        if (res == 9999) {
          uni.showModal({
            title: "提示",
            content: "演示环境:进入默认food店铺商家,确定进入？",
            success: function (res) {
              if (res.confirm) {
                uni.navigateTo({
                  url: item.pages,
                });
              } else if (res.cancel) {
                return;
              }
            },
          });

          return;
        }
        if (!res) {
          return;
        }
      }

      uni.navigateTo({
        url: item.pages,
      });
      break;
    case "miniprogram":
      uni.navigateToMiniProgram({
        appId: item.app_id,
      });
      break;
    case "menu":
      uni.navigateTo({
        url:
          "/pages/components/pages/mine/service?id=" +
          item.id +
          "&name=" +
          item.name,
      });
      break;
    case "content":
      uni.navigateTo({
        url:
          "/pages/components/pages/mine/content?id=" +
          item.id +
          "&name=" +
          item.name,
      });
      break;
  }
};
const goVip = () => {
  if (member.value.cardId > 0) {
    uni.navigateTo({
      url: "/pages/components/pages/vip/detail?id=" + member.value.cardId,
    });
  } else {
    uni.navigateTo({
      url: "/pages/components/pages/vip/vip",
    });
  }
};
</script>

<style lang="scss" scoped>
page {
  height: auto;
  min-height: 100%;
}

.my-background {
  background: linear-gradient(
    -180deg,
    #b3e8fb 0,
    #ffffff 100%,
    #ffffff 100%
  ) !important;
}

.avatar-img {
  width: 100rpx;
  height: 100rpx;
}
.nick-text {
  font-size: 24rpx;
  color: #333;
  //font-weight: bold;
}
.id-text {
  font-size: 22rpx;
  color: #333;
}
.icon-img {
  width: 50rpx;
  height: 50rpx;
}
.vip-box {
  position: relative;
  text-align: center;
  .vip-box-content {
    height: 100rpx;
    .vip-box-left {
      position: absolute;
      top: 38rpx;
      left: 60rpx;
      z-index: 2;
    }
    .vip-box-right {
      color: #ffffff;
      position: absolute;
      top: 45rpx;
      right: 60rpx;
      z-index: 2;
    }
  }
}
.font-size-26 {
  font-size: 28rpx;
}
.font-size-24 {
  font-size: 24rpx;
}
.vip-bg-img {
  position: absolute;
  top: 10rpx;
  left: 0;
  right: 0;
  margin: auto;
  width: 700rpx;
}

.item-image {
  width: 40rpx;
  height: 40rpx;
}

.bg {
  width: 100%;
  height: calc(410 / 594 * 750rpx);
}

.hym-btn {
  position: absolute;
  top: 40rpx;
  right: 40rpx;
  color: $color-primary;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50rem;
  font-size: $font-size-sm;
  box-shadow: 0 0 20rpx rgba(66, 66, 66, 0.1);

  &::after {
    border: 0;
  }

  image {
    width: 30rpx;
    height: 30rpx;
    margin-right: 10rpx;
  }
}

.user-box {
  position: relative;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
  //margin-top: 40rpx;
  box-shadow: $box-shadow;
}

// .avatar {
// 	position: relative;
// 	margin-top: -35rpx;
// 	margin-left: 35rpx;
// 	margin-right: 35rpx;
// 	width: 160rpx;
// 	height: 160rpx;
// 	border-radius: 20rpx;
// 	display: flex;
// 	align-items: center;
// 	justify-content: center;
// 	background-color: #FFFFFF;
// 	box-shadow: 0 0 20rpx rgba($color: #000000, $alpha: 0.2);

// 	image {
// 		width: 140rpx;
// 		height: 140rpx;
// 		border-radius: 100%;
// 	}

// 	.badge {
// 		position: absolute;
// 		right: -10rpx;
// 		bottom: -10rpx;
// 		background-color: #FFFFFF;
// 		border-radius: 50rem;
// 		display: flex;
// 		align-items: center;
// 		justify-content: center;
// 		color: $color-warning;
// 		font-size: 24rpx;
// 		padding: 8rpx 16rpx;
// 		box-shadow: 0 0 20rpx rgba($color: #000000, $alpha: 0.2);

// 		image {
// 			width: 30rpx;
// 			height: 30rpx;
// 		}
// 	}
// }

.level-benefit {
  margin-left: 35rpx;
  padding: 10rpx 0 10rpx 30rpx;
  border-radius: 50rem 0 0 50rem;
}

.user-grid {
  width: 25%;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .value {
    margin-bottom: 20rpx;
  }
}

.level-benefit-box {
  border-radius: 8rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 10rpx 8rpx rgba($color: #878889, $alpha: 0.1);
  width: 100%;
  display: flex;
  padding: 30rpx;
  flex-direction: column;
  background-color: #ffffff;

  .row {
    display: flex;
    padding: 30rpx 0 20rpx;
    justify-content: space-around;
    align-items: center;

    .grid {
      width: 20%;
      display: flex;
      flex-direction: column;
      font-size: $font-size-sm;
      color: $text-color-assist;
      align-items: center;

      image {
        width: 80rpx;
        height: 80rpx;
        margin-bottom: 10rpx;
      }
    }
  }
}

.banner {
  width: 100%;
  border-radius: 8rpx;
  margin-bottom: 30rpx;
}

.service-box {
  width: 100%;
  background-color: #ffffff;
  padding: 32rpx 30rpx 10rpx;
  box-shadow: $box-shadow;

  // .row {
  // 	display: flex;
  // 	flex-wrap: wrap;
  // 	color: $text-color-assist;
  // 	font-size: $font-size-sm;
  // 	padding-bottom: -40rpx;

  // 	.grid {
  // 		display: flex;
  // 		flex-direction: column;
  // 		justify-content: center;
  // 		align-items: center;
  // 		margin-bottom: 40rpx;
  // 		width: 25%;
  // 		position: relative;

  // 		image {
  // 			width: 80rpx;
  // 			height: 80rpx;
  // 			margin-bottom: 20rpx;
  // 		}
  // 	}

  // 	.opacity-0 {
  // 		position: absolute;
  // 		width: 100%;
  // 		height: 100%;
  // 		opacity: 0;
  // 		z-index: 10;
  // 	}

  // }
}
</style>
