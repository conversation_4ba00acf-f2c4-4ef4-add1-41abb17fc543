import request from '@/config/axios'

export interface ShopDeskQueueVO {
  id?: number
  shopId: number
  cateId: number
  uid?: number
  nickname?: string
  mobile?: string
  peopleCount: number
  status?: number
  remark?: string
  shopName?: string
  cateName?: string
  queueNumber?: string
  callTime?: Date
  seatTime?: Date
  createTime?: Date
}

export interface ShopDeskQueuePageReqVO {
  shopId?: number
  cateId?: number
  uid?: number
  nickname?: string
  mobile?: string
  peopleCount?: number
  status?: number
  createTime?: Date[]
  pageNo?: number
  pageSize?: number
}

export interface ShopDeskQueueCreateReqVO {
  shopId: number
  cateId: number
  uid?: number
  nickname?: string
  mobile?: string
  peopleCount: number
  status?: number
  remark?: string
  appointmentTime?: Date
}

export interface ShopDeskQueueUpdateReqVO {
  id: number
  shopId: number
  cateId: number
  uid?: number
  nickname?: string
  mobile?: string
  peopleCount: number
  status?: number
  remark?: string
  appointmentTime?: Date
}

export interface QueueStatsVO {
  totalCount: number
  waitingCount: number
  calledCount: number
  seatedCount: number
  avgWaitTime: number
}

export interface QueueRulesVO {
  maxPeopleCount: number
  maxWaitTime: number
  autoCallInterval: number
  enablePriority: boolean
  enableAppointment: boolean
}

export interface VoiceSettingsVO {
  enabled: boolean
  volume: number
  speed: number
  voice: string
}

export interface NotificationSettingsVO {
  smsEnabled: boolean
  wechatEnabled: boolean
  emailEnabled: boolean
  advanceNotifyTime: number
}

// 创建排队叫号
export const createShopDeskQueue = async (data: ShopDeskQueueVO) => {
  return await request.post({ url: '/desk/shop-desk-queue/create', data })
}

// 更新排队叫号
export const updateShopDeskQueue = async (data: ShopDeskQueueVO) => {
  return await request.put({ url: '/desk/shop-desk-queue/update', data })
}

// 删除排队叫号
export const deleteShopDeskQueue = async (id: number) => {
  return await request.delete({ url: '/desk/shop-desk-queue/delete', params: { id } })
}

// 获取排队叫号详情
export const getShopDeskQueue = async (id: number) => {
  return await request.get({ url: '/desk/shop-desk-queue/get?id=' + id })
}

// 获取排队叫号分页
export const getShopDeskQueuePage = async (params: ShopDeskQueuePageReqVO) => {
  return await request.get({ url: '/desk/shop-desk-queue/page', params })
}

// 叫号操作
export const callNumber = async (id: number) => {
  return await request.post({ url: '/desk/shop-desk-queue/call-number', params: { id } })
}

// 入座操作
export const seatNumber = async (id: number) => {
  return await request.post({ url: '/desk/shop-desk-queue/seat-number', params: { id } })
}

// 过号操作
export const passNumber = async (id: number) => {
  return await request.post({ url: '/desk/shop-desk-queue/pass-number', params: { id } })
}

// 过号重新排队操作
export const requeueNumber = async (id: number) => {
  return await request.post({ url: '/desk/shop-desk-queue/requeue?id=' + id })
}

// 获取排队统计数据
export const getQueueStats = async (params: any) => {
  return await request.get({ url: '/desk/shop-desk-queue/stats', params })
}

// 获取排队等待时间统计
export const getWaitTimeStats = async (params: any) => {
  return await request.get({ url: '/desk/shop-desk-queue/stats/wait-time', params })
}

// 获取各时段排队人数统计
export const getHourlyStats = async (params: any) => {
  return await request.get({ url: '/desk/shop-desk-queue/stats/hourly', params })
}

// 保存排队规则设置
export const saveQueueRules = async (data: QueueRulesVO) => {
  return await request.post({ url: '/desk/shop-desk-queue/settings/rules', data })
}

// 获取排队规则设置
export const getQueueRules = async () => {
  return await request.get({ url: '/desk/shop-desk-queue/settings/rules' })
}

// 保存语音播报设置
export const saveVoiceSettings = async (data: VoiceSettingsVO) => {
  return await request.post({ url: '/desk/shop-desk-queue/settings/voice', data })
}

// 获取语音播报设置
export const getVoiceSettings = async () => {
  return await request.get({ url: '/desk/shop-desk-queue/settings/voice' })
}

// 保存消息推送设置
export const saveNotificationSettings = async (data: NotificationSettingsVO) => {
  return await request.post({ url: '/desk/shop-desk-queue/settings/notification', data })
}

// 获取消息推送设置
export const getNotificationSettings = async () => {
  return await request.get({ url: '/desk/shop-desk-queue/settings/notification' })
}

// 保存预约设置
export const saveAppointmentSettings = async (data: any) => {
  return await request.post({ url: '/desk/shop-desk-queue/settings/appointment', data })
}

// 获取预约设置
export const getAppointmentSettings = async () => {
  return await request.get({ url: '/desk/shop-desk-queue/settings/appointment' })
}

// 保存社交分享设置
export const saveSocialSettings = async (data: any) => {
  return await request.post({ url: '/desk/shop-desk-queue/settings/social', data })
}

// 获取社交分享设置
export const getSocialSettings = async () => {
  return await request.get({ url: '/desk/shop-desk-queue/settings/social' })
}

// 导出排队数据
export const exportQueueData = async (params: any) => {
  return await request.download({ url: '/desk/shop-desk-queue/export', params })
}

// 获取当前排队状态（当前叫号、前方等待人数）
export const getCurrentQueueStatus = async (shopId: number) => {
  return await request.get({ url: '/desk/shop-desk-queue/status?shopId=' + shopId })
}

// 获取预计等待时间
export const getEstimatedWaitTime = async (id: number) => {
  return await request.get({ url: '/desk/shop-desk-queue/wait-time?id=' + id })
}

// 申请插队
export const requestPriority = async (id: number, reason: string) => {
  return await request.post({ url: '/desk/shop-desk-queue/priority', data: { id, reason } })
}
