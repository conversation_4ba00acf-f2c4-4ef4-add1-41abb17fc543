package ddyy123.top.food.module.desk.enums;

import ddyy123.top.food.framework.common.exception.ErrorCode;

/**
 * 错误码枚举类
 */
public interface ErrorCodeConstants {

    // ========== 桌面分类相关错误码 ==========
    ErrorCode SHOP_DESK_CATEGORY_NOT_EXISTS = new ErrorCode(1001001001, "桌面分类不存在");
    ErrorCode SHOP_DESK_CATEGORY_PEOPLE_EXCEED = new ErrorCode(1001001002, "人数超过桌型最大容量");
    
    // ========== 排队相关错误码 ==========
    ErrorCode SHOP_DESK_QUEUE_NOT_EXISTS = new ErrorCode(1001002001, "排队记录不存在");
    ErrorCode SHOP_DESK_QUEUE_ALREADY_EXISTS = new ErrorCode(1001002002, "您已在该门店排队中，请勿重复排队");
    ErrorCode SHOP_DESK_QUEUE_STATUS_ERROR = new ErrorCode(1001002003, "排队状态不正确");
    ErrorCode SHOP_DESK_QUEUE_NOT_YOUR_TURN = new ErrorCode(1001002004, "还未轮到您用餐");
    ErrorCode SHOP_DESK_QUEUE_PERMISSION_DENIED = new ErrorCode(1001002005, "没有操作权限");
    
    // ========== 桌号相关错误码 ==========
    ErrorCode NOT_VALID_NUMBER = new ErrorCode(1001004001, "桌号必须为数字");
    ErrorCode DESK_VALID_NUMBER = new ErrorCode(1001004002, "开始桌号必须小于结束桌号");
    ErrorCode SHOP_DESK_NOT_EXISTS = new ErrorCode(1001004003, "桌号不存在");
    ErrorCode DESK_IMPORT_LIST_IS_EMPTY = new ErrorCode(1001004004, "导入桌号数据不能为空");
    
    // ========== 门店相关错误码 ==========
    ErrorCode SHOP_NOT_EXISTS = new ErrorCode(1001003001, "门店不存在");
}
