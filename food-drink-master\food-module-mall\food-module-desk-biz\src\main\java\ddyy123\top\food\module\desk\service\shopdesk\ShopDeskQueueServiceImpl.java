package ddyy123.top.food.module.desk.service.shopdesk;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import ddyy123.top.food.framework.common.constant.ShopConstants;
import ddyy123.top.food.framework.common.pojo.PageResult;
import ddyy123.top.food.framework.mybatis.core.query.LambdaQueryWrapperX;
import ddyy123.top.food.framework.security.core.util.SecurityFrameworkUtils;
import ddyy123.top.food.module.desk.controller.admin.shopdesk.vo.queue.ShopDeskQueueCreateReqVO;
import ddyy123.top.food.module.desk.controller.admin.shopdesk.vo.queue.ShopDeskQueuePageReqVO;
import ddyy123.top.food.module.desk.controller.admin.shopdesk.vo.queue.ShopDeskQueueUpdateReqVO;
import ddyy123.top.food.module.desk.controller.app.shopdesk.vo.queue.AppShopDeskQueueCreateReqVO;
import ddyy123.top.food.module.desk.controller.app.shopdesk.vo.queue.AppShopDeskQueueListRespVO;
import ddyy123.top.food.module.desk.controller.app.shopdesk.vo.queue.AppShopDeskQueueRespVO;
import ddyy123.top.food.module.desk.convert.shopdesk.ShopDeskQueueConvert;
import ddyy123.top.food.module.desk.dal.dataobject.shopdesk.ShopDeskQueueDO;
import ddyy123.top.food.module.desk.dal.dataobject.shopdeskcategory.ShopDeskCategoryDO;
import ddyy123.top.food.module.desk.dal.mysql.shopdesk.ShopDeskQueueMapper;
import ddyy123.top.food.module.desk.enums.QueueStatusEnum;
import ddyy123.top.food.module.desk.service.shopdeskcategory.ShopDeskCategoryService;
import ddyy123.top.food.module.member.dal.dataobject.user.MemberUserDO;
import ddyy123.top.food.module.member.service.user.MemberUserService;
import ddyy123.top.food.module.message.redismq.msg.QueueMsg;
import ddyy123.top.food.module.store.dal.dataobject.storeshop.StoreShopDO;
import ddyy123.top.food.module.store.service.storeshop.StoreShopService;
import ddyy123.top.food.module.system.api.user.AdminUserApi;
import ddyy123.top.food.module.system.api.user.dto.AdminUserRespDTO;
import jakarta.annotation.Resource;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static ddyy123.top.food.framework.common.exception.util.ServiceExceptionUtil.exception;
import static ddyy123.top.food.module.desk.enums.ErrorCodeConstants.*;

/**
 * 门店排队叫号 Service 实现类
 */
@Service
@Validated
public class ShopDeskQueueServiceImpl extends ServiceImpl<ShopDeskQueueMapper, ShopDeskQueueDO> implements ShopDeskQueueService {

    @Resource
    private ShopDeskCategoryService shopDeskCategoryService;
    @Resource
    private StoreShopService storeShopService;
    @Resource
    private MemberUserService memberUserService;
    @Resource
    private AdminUserApi adminUserApi;
    @Resource
    private RedissonClient redissonClient;

    @Override
    public Long createShopDeskQueue(ShopDeskQueueCreateReqVO createReqVO) {
        // 校验桌面分类存在
        ShopDeskCategoryDO category = shopDeskCategoryService.getShopDeskCategory(createReqVO.getCateId());
        if (category == null) {
            throw exception(SHOP_DESK_CATEGORY_NOT_EXISTS);
        }

        // 校验门店存在
        StoreShopDO shop = storeShopService.getShop(createReqVO.getShopId());
        if (shop == null) {
            throw exception(SHOP_NOT_EXISTS);
        }

        // 生成排队号码
        String queueNumber = generateQueueNumber(createReqVO.getShopId(), createReqVO.getCateId());

        // 创建排队记录
        ShopDeskQueueDO shopDeskQueue = ShopDeskQueueConvert.INSTANCE.convert(createReqVO);
        shopDeskQueue.setShopName(shop.getName());
        shopDeskQueue.setCateName(category.getName());
        shopDeskQueue.setQueueNumber(queueNumber);
        shopDeskQueue.setStatus(QueueStatusEnum.QUEUE_STATUS_1.getValue());

        // 获取管理员信息
        AdminUserRespDTO user = adminUserApi.getUser(SecurityFrameworkUtils.getLoginUserId());
        if (user != null) {
            shopDeskQueue.setNickname(user.getNickname());
        }

        baseMapper.insert(shopDeskQueue);
        return shopDeskQueue.getId();
    }

    @Override
    public void updateShopDeskQueue(ShopDeskQueueUpdateReqVO updateReqVO) {
        // 校验存在
        validateShopDeskQueueExists(updateReqVO.getId());
        // 更新
        ShopDeskQueueDO updateObj = ShopDeskQueueConvert.INSTANCE.convert(updateReqVO);
        baseMapper.updateById(updateObj);
    }

    @Override
    public void deleteShopDeskQueue(Long id) {
        // 校验存在
        validateShopDeskQueueExists(id);
        // 删除
        baseMapper.deleteById(id);
    }

    private void validateShopDeskQueueExists(Long id) {
        if (baseMapper.selectById(id) == null) {
            throw exception(SHOP_DESK_QUEUE_NOT_EXISTS);
        }
    }

    @Override
    public ShopDeskQueueDO getShopDeskQueue(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public PageResult<ShopDeskQueueDO> getShopDeskQueuePage(ShopDeskQueuePageReqVO pageReqVO) {
        return baseMapper.selectPage(pageReqVO);
    }

    @Override
    public AppShopDeskQueueRespVO takeNumber(AppShopDeskQueueCreateReqVO createReqVO) {
        // 校验门店存在
        StoreShopDO shop = storeShopService.getShop(createReqVO.getShopId());
        if (shop == null) {
            throw exception(SHOP_NOT_EXISTS);
        }

        // 校验用户是否已经在排队中
        if (getUserQueue(createReqVO.getShopId(), null) != null) {
            throw exception(SHOP_DESK_QUEUE_ALREADY_EXISTS);
        }

        // 根据人数自动分配桌面类型
        ShopDeskCategoryDO category;
        if (createReqVO.getCateId() == null) {
            // 如果没有指定分类，则根据人数自动选择最合适的桌型
            category = shopDeskCategoryService.findSuitableDeskCategory(createReqVO.getShopId(), createReqVO.getPeopleCount());
            if (category == null) {
                throw exception(SHOP_DESK_CATEGORY_NOT_EXISTS, "该门店没有合适的桌型");
            }
        } else {
            // 如果指定了分类，则使用指定的分类
            category = shopDeskCategoryService.getShopDeskCategory(createReqVO.getCateId());
            if (category == null) {
                throw exception(SHOP_DESK_CATEGORY_NOT_EXISTS);
            }
            // 检查人数是否超过桌型容量
            if (createReqVO.getPeopleCount() > category.getPeople()) {
                throw exception(SHOP_DESK_CATEGORY_PEOPLE_EXCEED, category.getPeople());
            }
        }

        // 生成排队号码
        String queueNumber = generateQueueNumber(createReqVO.getShopId(), category.getId());

        // 创建排队记录
        ShopDeskQueueDO shopDeskQueue = ShopDeskQueueConvert.INSTANCE.convert(createReqVO);
        shopDeskQueue.setShopName(shop.getName());
        shopDeskQueue.setCateName(category.getName());
        shopDeskQueue.setQueueNumber(queueNumber);
        shopDeskQueue.setStatus(QueueStatusEnum.QUEUE_STATUS_1.getValue());
        shopDeskQueue.setUid(createReqVO.getUid());

        // 获取用户信息
        MemberUserDO user = memberUserService.getUser(createReqVO.getUid());
        if (user != null) {
            shopDeskQueue.setNickname(user.getNickname());
            shopDeskQueue.setMobile(user.getMobile());
        }

        baseMapper.insert(shopDeskQueue);

        // 返回排队信息
        return buildQueueRespVO(shopDeskQueue);
    }

    @Override
    public void cancelQueue(Long id) {
        // 校验存在
        ShopDeskQueueDO queue = validateQueueAndCheckPermission(id);

        // 只有排队中和已叫号的状态才能取消
        if (!QueueStatusEnum.QUEUE_STATUS_1.getValue().equals(queue.getStatus()) &&
                !QueueStatusEnum.QUEUE_STATUS_2.getValue().equals(queue.getStatus())) {
            throw exception(SHOP_DESK_QUEUE_STATUS_ERROR);
        }

        // 更新状态为已取消
        queue.setStatus(QueueStatusEnum.QUEUE_STATUS_3.getValue());
        baseMapper.updateById(queue);
    }

    @Override
    public void callNumber(Long id) {
        // 校验存在
        ShopDeskQueueDO queue = validateQueueExists(id);

        // 只有排队中的状态才能叫号
        if (!QueueStatusEnum.QUEUE_STATUS_1.getValue().equals(queue.getStatus())) {
            throw exception(SHOP_DESK_QUEUE_STATUS_ERROR);
        }

        // 更新状态为已叫号
        queue.setStatus(QueueStatusEnum.QUEUE_STATUS_2.getValue());
        queue.setCallTime(LocalDateTime.now());
        baseMapper.updateById(queue);

        // 发送叫号通知消息
        sendQueueCallNotify(queue);
    }

    @Override
    public void passNumber(Long id) {
        // 校验存在
        ShopDeskQueueDO queue = validateQueueExists(id);

        // 只有已叫号的状态才能过号
        if (!QueueStatusEnum.QUEUE_STATUS_2.getValue().equals(queue.getStatus())) {
            throw exception(SHOP_DESK_QUEUE_STATUS_ERROR);
        }

        // 更新状态为已过号
        queue.setStatus(QueueStatusEnum.QUEUE_STATUS_4.getValue());
        baseMapper.updateById(queue);
    }

    @Override
    public void seatNumber(Long id) {
        // 校验存在
        ShopDeskQueueDO queue = validateQueueExists(id);

        // 只有已叫号的状态才能入座
        if (!QueueStatusEnum.QUEUE_STATUS_2.getValue().equals(queue.getStatus())) {
            throw exception(SHOP_DESK_QUEUE_STATUS_ERROR);
        }

        // 更新状态为已入座
        queue.setStatus(QueueStatusEnum.QUEUE_STATUS_5.getValue());
        queue.setSeatTime(LocalDateTime.now());
        baseMapper.updateById(queue);
    }

    @Override
    public AppShopDeskQueueRespVO getCurrentQueue(Long shopId) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        ShopDeskQueueDO queue = baseMapper.selectOne(new LambdaQueryWrapperX<ShopDeskQueueDO>()
                .eq(ShopDeskQueueDO::getUid, userId)
                .eq(ShopDeskQueueDO::getShopId, shopId)
                .in(ShopDeskQueueDO::getStatus, QueueStatusEnum.QUEUE_STATUS_1.getValue(),
                        QueueStatusEnum.QUEUE_STATUS_2.getValue())
                .orderByDesc(ShopDeskQueueDO::getId)
                .last("LIMIT 1"));

        if (queue == null) {
            return null;
        }

        return buildQueueRespVO(queue);
    }

    @Override
    @Transactional(readOnly = true)
    public AppShopDeskQueueListRespVO getShopQueueList(Long shopId) {
        // 1. 获取当前登录用户
        Long userId = SecurityFrameworkUtils.getLoginUserId();

        // 2. 查询当前用户在当前店铺的排队信息
        AppShopDeskQueueRespVO currentQueue = null;
        ShopDeskQueueDO userQueue = baseMapper.selectOne(new LambdaQueryWrapperX<ShopDeskQueueDO>()
                .eq(ShopDeskQueueDO::getShopId, shopId)
                .eq(ShopDeskQueueDO::getUid, userId)
                .in(ShopDeskQueueDO::getStatus,
                        QueueStatusEnum.QUEUE_STATUS_1.getValue(),
                        QueueStatusEnum.QUEUE_STATUS_2.getValue())
                .orderByDesc(ShopDeskQueueDO::getCreateTime)
                .last("LIMIT 1"));

        if (userQueue != null) {
            currentQueue = buildQueueRespVO(userQueue);
        }

        // 3. 获取所有排队中的记录
        List<ShopDeskQueueDO> queueList = baseMapper.selectList(new LambdaQueryWrapperX<ShopDeskQueueDO>()
                .eq(ShopDeskQueueDO::getShopId, shopId)
                .in(ShopDeskQueueDO::getStatus,
                        QueueStatusEnum.QUEUE_STATUS_1.getValue(),
                        QueueStatusEnum.QUEUE_STATUS_2.getValue())
                .orderByAsc(ShopDeskQueueDO::getCreateTime));

        // 4. 按分类统计排队信息
        List<AppShopDeskQueueListRespVO.CateQueueCount> cateQueueCounts = new ArrayList<>();
        shopDeskCategoryService.getShopDeskCategoryList(shopId).forEach(category -> {
            AppShopDeskQueueListRespVO.CateQueueCount count = new AppShopDeskQueueListRespVO.CateQueueCount();
            count.setCateId(category.getId());
            count.setCateName(category.getName());

            long queueCount = queueList.stream()
                    .filter(queue -> queue.getCateId().equals(category.getId()))
                    .count();
            count.setQueueCount((int) queueCount);

            cateQueueCounts.add(count);
        });

        // 5. 构建返回结果
        AppShopDeskQueueListRespVO respVO = new AppShopDeskQueueListRespVO();
        respVO.setCurrentQueue(currentQueue);
        respVO.setCateQueueCounts(cateQueueCounts);

        // 6. 转换排队记录为VO对象
        List<AppShopDeskQueueRespVO> queueVOs = queueList.stream()
                .map(this::buildQueueRespVO)
                .collect(Collectors.toList());
        respVO.setQueueList(queueVOs);
        respVO.setTotal(queueVOs.size());

        return respVO;
    }

    @Override
    public ShopDeskQueueDO getUserQueue(Long shopId, Long deskId) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        return baseMapper.selectOne(new LambdaQueryWrapperX<ShopDeskQueueDO>()
                .eq(ShopDeskQueueDO::getUid, userId)
                .eq(ShopDeskQueueDO::getShopId, shopId)

                .in(ShopDeskQueueDO::getStatus, QueueStatusEnum.QUEUE_STATUS_1.getValue(),
                        QueueStatusEnum.QUEUE_STATUS_2.getValue()));
    }

    /**
     * 校验排队记录存在，并检查权限
     *
     * @param id 排队记录ID
     * @return 排队记录
     */
    private ShopDeskQueueDO validateQueueAndCheckPermission(Long id) {
        ShopDeskQueueDO queue = validateQueueExists(id);

        // 检查是否是当前用户的排队记录
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        if (!userId.equals(queue.getUid())) {
            throw exception(SHOP_DESK_QUEUE_PERMISSION_DENIED);
        }

        return queue;
    }

    /**
     * 校验排队记录存在
     *
     * @param id 排队记录ID
     * @return 排队记录
     */
    private ShopDeskQueueDO validateQueueExists(Long id) {
        ShopDeskQueueDO queue = baseMapper.selectById(id);
        if (queue == null) {
            throw exception(SHOP_DESK_QUEUE_NOT_EXISTS);
        }
        return queue;
    }

    /**
     * 生成排队号码
     *
     * @param shopId 门店ID
     * @param cateId 分类ID
     * @return 排队号码
     */
    private String generateQueueNumber(Long shopId, Long cateId) {
        // 获取当天该分类的排队数量
        LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        int count = baseMapper.selectCount(new LambdaQueryWrapperX<ShopDeskQueueDO>()
                .eq(ShopDeskQueueDO::getShopId, shopId)
                .eq(ShopDeskQueueDO::getCateId, cateId)
                .ge(ShopDeskQueueDO::getCreateTime, today)).intValue();

        // 生成号码：分类首字母+日期+序号，例如：A0601001
        ShopDeskCategoryDO category = shopDeskCategoryService.getShopDeskCategory(cateId);
        String prefix = "A"; // 默认前缀
        if (category != null && StrUtil.isNotEmpty(category.getName())) {
            prefix = category.getName().substring(0, 1).toUpperCase();
        }

        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("MMdd"));
        return prefix + dateStr + String.format("%03d", count + 1);
    }

    /**
     * 构建排队信息响应VO
     *
     * @param queue 排队记录
     * @return 排队信息响应VO
     */
    private AppShopDeskQueueRespVO buildQueueRespVO(ShopDeskQueueDO queue) {
        AppShopDeskQueueRespVO respVO = ShopDeskQueueConvert.INSTANCE.convertApp(queue);

        // 查询排队中的记录，计算前面还有多少人
        int waitCount = baseMapper.selectCount(new LambdaQueryWrapperX<ShopDeskQueueDO>()
                .eq(ShopDeskQueueDO::getShopId, queue.getShopId())
                .eq(ShopDeskQueueDO::getCateId, queue.getCateId())
                .eq(ShopDeskQueueDO::getStatus, QueueStatusEnum.QUEUE_STATUS_1.getValue())
                .lt(ShopDeskQueueDO::getId, queue.getId())).intValue();
        respVO.setWaitCount(waitCount);

        // 设置状态描述
        QueueStatusEnum statusEnum = QueueStatusEnum.toType(queue.getStatus());
        if (statusEnum != null) {
            respVO.setStatusDesc(statusEnum.getDesc());
        }

        return respVO;
    }

    /**
     * 发送排队叫号通知
     *
     * @param queue 排队记录
     */
    private void sendQueueCallNotify(ShopDeskQueueDO queue) {
        try {
            QueueMsg queueMsg = QueueMsg.builder()
                    .queueId(queue.getId())
                    .shopId(queue.getShopId())
                    .queueNumber(queue.getQueueNumber())
                    .build();

            RBlockingQueue<QueueMsg> blockingQueue = redissonClient.getBlockingQueue(ShopConstants.REDIS_QUEUE_CALL_NOTIFY);
            blockingQueue.offer(queueMsg);
        } catch (Exception e) {
            log.error("发送排队叫号通知失败", e);
        }
    }

    /**
     * 发送排队自动过号通知
     *
     * @param queue        排队记录
     * @param delaySeconds 延迟秒数
     */
    private void sendQueueAutoPassNotify(ShopDeskQueueDO queue, long delaySeconds) {
        try {
            QueueMsg queueMsg = QueueMsg.builder()
                    .queueId(queue.getId())
                    .shopId(queue.getShopId())
                    .queueNumber(queue.getQueueNumber())
                    .build();

            RBlockingQueue<QueueMsg> blockingQueue = redissonClient.getBlockingQueue(ShopConstants.REDIS_QUEUE_AUTO_PASS);
            blockingQueue.offer(queueMsg);
        } catch (Exception e) {
            log.error("发送排队自动过号通知失败", e);
        }
    }
}