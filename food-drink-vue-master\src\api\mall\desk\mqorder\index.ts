import request from '@/config/axios'

// 门店排队叫号相关的数据类型定义
export interface ShopDeskQueueVO {
  id?: number
  shopId: number
  cateId: number
  uid?: number
  nickname?: string
  mobile?: string
  peopleCount: number
  status?: number
  remark?: string
  shopName?: string
  cateName?: string
  queueNumber?: string
  callTime?: Date
  seatTime?: Date
  createTime?: Date
}

// 创建门店排队叫号请求参数
export interface ShopDeskQueueCreateReqVO {
  shopId: number
  cateId: number
  uid?: number
  nickname?: string
  mobile?: string
  peopleCount: number
  status?: number
  remark?: string
}

// 更新门店排队叫号请求参数
export interface ShopDeskQueueUpdateReqVO {
  id: number
  shopId: number
  cateId: number
  uid?: number
  nickname?: string
  mobile?: string
  peopleCount: number
  status?: number
  remark?: string
}

// 获取门店排队叫号分页请求参数
export interface ShopDeskQueuePageReqVO {
  shopId?: number
  cateId?: number
  uid?: number
  nickname?: string
  mobile?: string
  peopleCount?: number
  status?: number
  createTime?: Date[]
  pageNo?: number
  pageSize?: number
}

// 分页结果
export interface PageResult<T> {
  list: T[]
  total: number
}

// 通用结果
export interface Result<T> {
  code: number
  data: T
  msg: string
}

/**
 * 创建门店排队叫号
 * @param data 创建参数
 * @returns 创建结果
 */
export function createShopDeskQueue(data: ShopDeskQueueCreateReqVO) {
  return request({
    url: '/admin-api/desk/shop-desk-queue/create',
    method: 'post',
    data
  })
}

/**
 * 更新门店排队叫号
 * @param data 更新参数
 * @returns 更新结果
 */
export function updateShopDeskQueue(data: ShopDeskQueueUpdateReqVO) {
  return request({
    url: '/admin-api/desk/shop-desk-queue/update',
    method: 'put',
    data
  })
}

/**
 * 删除门店排队叫号
 * @param id 排队叫号ID
 * @returns 删除结果
 */
export function deleteShopDeskQueue(id: number) {
  return request({
    url: '/admin-api/desk/shop-desk-queue/delete',
    method: 'delete',
    params: { id }
  })
}

/**
 * 获取门店排队叫号
 * @param id 排队叫号ID
 * @returns 排队叫号信息
 */
export function getShopDeskQueue(id: number) {
  return request<Result<ShopDeskQueueVO>>({
    url: '/admin-api/desk/shop-desk-queue/get',
    method: 'get',
    params: { id }
  })
}

/**
 * 获取门店排队叫号分页
 * @param params 查询参数
 * @returns 分页结果
 */
export function getShopDeskQueuePage(params: ShopDeskQueuePageReqVO) {
  return request<Result<PageResult<ShopDeskQueueVO>>>({ 
    url: '/admin-api/desk/shop-desk-queue/page',
    method: 'get',
    params
  })
}

/**
 * 叫号
 * @param id 排队叫号ID
 * @returns 操作结果
 */
export function callNumber(id: number) {
  return request({
    url: '/admin-api/desk/shop-desk-queue/call-number',
    method: 'post',
    params: { id }
  })
}

/**
 * 入座
 * @param id 排队叫号ID
 * @returns 操作结果
 */
export function seatNumber(id: number) {
  return request({
    url: '/admin-api/desk/shop-desk-queue/seat-number',
    method: 'post',
    params: { id }
  })
}

/**
 * 过号
 * @param id 排队叫号ID
 * @returns 操作结果
 */
export function passNumber(id: number) {
  return request({
    url: '/admin-api/desk/shop-desk-queue/pass-number',
    method: 'post',
    params: { id }
  })
}