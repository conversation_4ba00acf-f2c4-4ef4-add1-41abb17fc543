<template>
  <Dialog title="详情" v-model="dialogVisible">
    <el-descriptions :column="1" border>
      <el-descriptions-item label="排队号码">
        {{ detailData.id }}
      </el-descriptions-item>
      <el-descriptions-item label="门店名称">
        {{ getShopName(detailData.shopId) }}
      </el-descriptions-item>
      <el-descriptions-item label="桌面分类">
        {{ getCateName(detailData.cateId) }}
      </el-descriptions-item>
      <el-descriptions-item label="用户名称">
        {{ detailData.nickname }}
      </el-descriptions-item>
      <el-descriptions-item label="手机号">
        {{ detailData.mobile }}
      </el-descriptions-item>
      <el-descriptions-item label="就餐人数">
        {{ detailData.peopleCount }} 人
      </el-descriptions-item>
      <el-descriptions-item label="排队状态">
        <el-tag :type="getStatusType(detailData.status)">
          {{ getStatusLabel(detailData.status) }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="等待时间">
        {{ formatWaitTime(detailData.createTime) }}
      </el-descriptions-item>
      <el-descriptions-item label="预约时间" v-if="detailData.appointmentTime">
        {{ formatDate(detailData.appointmentTime) }}
      </el-descriptions-item>
      <el-descriptions-item label="创建时间">
        {{ formatDate(detailData.createTime) }}
      </el-descriptions-item>
      <el-descriptions-item label="备注" :span="1">
        {{ detailData.remark || '无' }}
      </el-descriptions-item>
    </el-descriptions>
    
    <!-- 排队进度信息 -->
    <el-divider content-position="center">排队进度</el-divider>
    <el-descriptions :column="1" border>
      <el-descriptions-item label="当前叫号">
        {{ queueStatus.currentNumber || '暂无叫号' }}
      </el-descriptions-item>
      <el-descriptions-item label="前方等待人数">
        {{ queueStatus.waitingCount }} 人
      </el-descriptions-item>
      <el-descriptions-item label="预计等待时间">
        {{ queueStatus.estimatedWaitTime }} 分钟
      </el-descriptions-item>
      <el-descriptions-item label="优先级">
        <el-tag :type="getPriorityType(queueStatus.priority)">
          {{ getPriorityLabel(queueStatus.priority) }}
        </el-tag>
      </el-descriptions-item>
    </el-descriptions>
    
    <!-- 排队历史记录 -->
    <el-divider content-position="center">状态变更记录</el-divider>
    <el-timeline>
      <el-timeline-item
        v-for="(activity, index) in queueHistory"
        :key="index"
        :timestamp="activity.time"
        :type="activity.type"
      >
        {{ activity.content }}
      </el-timeline-item>
    </el-timeline>
    
    <template #footer>
      <el-button @click="dialogVisible = false">关 闭</el-button>
      <el-button
        v-if="detailData.status === 1"
        type="primary"
        @click="handleCallNumber"
      >
        叫 号
      </el-button>
      <el-button
        v-if="detailData.status === 2"
        type="success"
        @click="handleSeatNumber"
      >
        入 座
      </el-button>
      <el-button
        v-if="detailData.status === 4"
        type="warning"
        @click="handleRequeue"
      >
        重新排队
      </el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, defineEmits, defineExpose } from 'vue'
import { formatDate } from '@/utils/formatTime'
import * as ShopDeskQueueApi from '@/api/mall/desk/mqorder'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const detailLoading = ref(false) // 详情数据的加载中

// 门店选项
const shopOptions = [
  { id: 1, name: '总店' },
  { id: 2, name: '北京分店' },
  { id: 3, name: '上海分店' }
]

// 桌面分类选项
const cateOptions = [
  { id: 1, name: '大桌' },
  { id: 2, name: '中桌' },
  { id: 3, name: '小桌' },
  { id: 4, name: 'VIP包间' }
]

// 排队状态选项
const statusOptions = [
  { label: '排队中', value: 1, type: 'info' },
  { label: '已叫号', value: 2, type: 'warning' },
  { label: '已取消', value: 3, type: 'danger' },
  { label: '已过号', value: 4, type: 'danger' },
  { label: '已入座', value: 5, type: 'success' }
]

// 优先级选项
const priorityOptions = [
  { label: '普通', value: 0, type: '' },
  { label: '优先', value: 1, type: 'warning' },
  { label: 'VIP', value: 2, type: 'danger' }
]

// 详情数据
const detailData = ref<any>({
  id: 0,
  shopId: 0,
  cateId: 0,
  uid: 0,
  nickname: '',
  mobile: '',
  peopleCount: 0,
  status: 0,
  remark: '',
  createTime: '',
  appointmentTime: ''
})

// 排队状态信息
const queueStatus = ref({
  currentNumber: '',
  waitingCount: 0,
  estimatedWaitTime: 0,
  priority: 0
})

// 排队历史记录
const queueHistory = ref([
  { content: '创建排队记录', time: '2023-05-01 10:00:00', type: 'primary' },
  { content: '系统叫号', time: '2023-05-01 10:30:00', type: 'warning' },
  { content: '顾客入座', time: '2023-05-01 10:35:00', type: 'success' }
])

/** 获取门店名称 */
const getShopName = (shopId) => {
  const shop = shopOptions.find(item => item.id === shopId)
  return shop ? shop.name : '未知门店'
}

/** 获取桌面分类名称 */
const getCateName = (cateId) => {
  const cate = cateOptions.find(item => item.id === cateId)
  return cate ? cate.name : '未知分类'
}

/** 获取状态标签 */
const getStatusLabel = (status) => {
  const statusObj = statusOptions.find(item => item.value === status)
  return statusObj ? statusObj.label : '未知状态'
}

/** 获取状态类型 */
const getStatusType = (status) => {
  const statusObj = statusOptions.find(item => item.value === status)
  return statusObj ? statusObj.type : ''
}

/** 获取优先级标签 */
const getPriorityLabel = (priority) => {
  const priorityObj = priorityOptions.find(item => item.value === priority)
  return priorityObj ? priorityObj.label : '普通'
}

/** 获取优先级类型 */
const getPriorityType = (priority) => {
  const priorityObj = priorityOptions.find(item => item.value === priority)
  return priorityObj ? priorityObj.type : ''
}

/** 格式化等待时间 */
const formatWaitTime = (createTime) => {
  if (!createTime) return '0 分钟'
  const createDate = new Date(createTime)
  const now = new Date()
  const diffMinutes = Math.floor((now.getTime() - createDate.getTime()) / (1000 * 60))
  
  if (diffMinutes < 60) {
    return `${diffMinutes} 分钟`
  } else {
    const hours = Math.floor(diffMinutes / 60)
    const minutes = diffMinutes % 60
    return `${hours} 小时 ${minutes} 分钟`
  }
}

/** 打开弹窗 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const open = async (id: number) => {
  dialogVisible.value = true
  await getDetail(id)
}

/** 获取详情 */
const getDetail = async (id: number) => {
  detailLoading.value = true
  try {
    const data = await ShopDeskQueueApi.getShopDeskQueue(id)
    detailData.value = data

    // 模拟获取排队状态信息
    // 实际项目中，这些数据应该从后端获取
    queueStatus.value = {
      currentNumber: `A${Math.floor(Math.random() * 100)}`,
      waitingCount: Math.floor(Math.random() * 10),
      estimatedWaitTime: Math.floor(Math.random() * 30) + 5,
      priority: Math.floor(Math.random() * 3)
    }

    // 模拟获取排队历史记录
    // 实际项目中，这些数据应该从后端获取
    const createTime = new Date(detailData.value.createTime)
    queueHistory.value = [
      {
        content: '创建排队记录',
        time: formatDate(createTime),
        type: 'primary'
      }
    ]
    
    if (detailData.value.status >= 1) {
      const callTime = new Date(createTime.getTime() + 30 * 60 * 1000)
      queueHistory.value.push({
        content: '系统叫号', 
        time: formatDate(callTime), 
        type: 'warning'
      })
    }
    
    if (detailData.value.status >= 2) {
      const seatTime = new Date(createTime.getTime() + 35 * 60 * 1000)
      queueHistory.value.push({
        content: '顾客入座', 
        time: formatDate(seatTime), 
        type: 'success'
      })
    }
    
    if (detailData.value.status === 3) {
      const cancelTime = new Date(createTime.getTime() + 20 * 60 * 1000)
      queueHistory.value.push({
        content: '顾客取消', 
        time: formatDate(cancelTime), 
        type: 'danger'
      })
    }
    
    if (detailData.value.status === 4) {
      const missTime = new Date(createTime.getTime() + 40 * 60 * 1000)
      queueHistory.value.push({
        content: '顾客过号', 
        time: formatDate(missTime), 
        type: 'danger'
      })
    }
  } catch (error) {
    console.error('获取排队叫号详情失败:', error)
    message.error('获取排队叫号详情失败')
  } finally {
    detailLoading.value = false
  }
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 叫号操作 */
const handleCallNumber = async () => {
  try {
    await ShopDeskQueueApi.callNumber(detailData.value.id)
    message.success('叫号成功')
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('叫号失败:', error)
    message.error('叫号失败')
  }
}

/** 入座操作 */
const handleSeatNumber = async () => {
  try {
    await ShopDeskQueueApi.seatNumber(detailData.value.id)
    message.success('入座成功')
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('入座失败:', error)
    message.error('入座失败')
  }
}

/** 重新排队操作 */
const handleRequeue = async () => {
  try {
    // 这里应该调用重新排队的API
    // 由于API中没有提供，这里模拟一个更新操作
    await ShopDeskQueueApi.updateShopDeskQueue({
      id: detailData.value.id,
      shopId: detailData.value.shopId,
      cateId: detailData.value.cateId,
      uid: detailData.value.uid,
      nickname: detailData.value.nickname,
      mobile: detailData.value.mobile,
      peopleCount: detailData.value.peopleCount,
      status: 0, // 重新设置为等待中
      remark: detailData.value.remark
    })
    message.success('重新排队成功')
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('重新排队失败:', error)
    message.error('重新排队失败')
  }
}
</script>