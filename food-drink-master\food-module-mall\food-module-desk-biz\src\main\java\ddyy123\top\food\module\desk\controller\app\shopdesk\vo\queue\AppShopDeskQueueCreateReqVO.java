package ddyy123.top.food.module.desk.controller.app.shopdesk.vo.queue;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 用户 APP - 门店排队叫号创建 Request VO
 */
@Data
@Schema(description = "用户 APP - 门店排队叫号创建 Request VO")
public class AppShopDeskQueueCreateReqVO {

    @Schema(description = "门店ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "门店ID不能为空")
    private Long shopId;

    @Schema(description = "桌面分类ID")
    private Long cateId;

    @Schema(description = "就餐人数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "就餐人数不能为空")
    private Integer peopleCount;

    @Schema(description = "备注")
    private String remark;

    private Long uid;
}