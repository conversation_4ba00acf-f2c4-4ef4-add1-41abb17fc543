package ddyy123.top.food.module.desk.service.shopdeskcategory;

import ddyy123.top.food.framework.mybatis.core.query.LambdaQueryWrapperX;
import ddyy123.top.food.module.store.dal.dataobject.storeshop.StoreShopDO;
import ddyy123.top.food.module.store.dal.mysql.storeshop.StoreShopMapper;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import ddyy123.top.food.module.desk.controller.admin.shopdeskcategory.vo.*;
import ddyy123.top.food.module.desk.dal.dataobject.shopdeskcategory.ShopDeskCategoryDO;
import ddyy123.top.food.framework.common.pojo.PageResult;
import ddyy123.top.food.framework.common.util.object.BeanUtils;

import ddyy123.top.food.module.desk.dal.mysql.shopdeskcategory.ShopDeskCategoryMapper;

import java.util.List;

import static ddyy123.top.food.framework.common.exception.util.ServiceExceptionUtil.exception;
import static ddyy123.top.food.module.desk.enums.ErrorCodeConstants.*;

/**
 * 门店桌号分类 Service 实现类
 *
 */
@Service
@Validated
public class ShopDeskCategoryServiceImpl implements ShopDeskCategoryService {

    @Resource
    private ShopDeskCategoryMapper shopDeskCategoryMapper;
    @Resource
    private StoreShopMapper shopMapper;

    @Override
    public Long createShopDeskCategory(ShopDeskCategorySaveReqVO createReqVO) {
        // 插入
        ShopDeskCategoryDO shopDeskCategory = BeanUtils.toBean(createReqVO, ShopDeskCategoryDO.class);
        StoreShopDO storeShopDO = this.getShop(createReqVO.getShopId());
        shopDeskCategory.setShopName(storeShopDO.getName());
        shopDeskCategoryMapper.insert(shopDeskCategory);
        // 返回
        return shopDeskCategory.getId();
    }

    @Override
    public void updateShopDeskCategory(ShopDeskCategorySaveReqVO updateReqVO) {
        // 校验存在
        validateShopDeskCategoryExists(updateReqVO.getId());
        // 更新
        ShopDeskCategoryDO updateObj = BeanUtils.toBean(updateReqVO, ShopDeskCategoryDO.class);
        StoreShopDO storeShopDO = this.getShop(updateReqVO.getShopId());
        updateObj.setShopName(storeShopDO.getName());
        shopDeskCategoryMapper.updateById(updateObj);
    }

    @Override
    public void deleteShopDeskCategory(Long id) {
        // 校验存在
        validateShopDeskCategoryExists(id);
        // 删除
        shopDeskCategoryMapper.deleteById(id);
    }

    private void validateShopDeskCategoryExists(Long id) {
        if (shopDeskCategoryMapper.selectById(id) == null) {
            throw exception(SHOP_DESK_CATEGORY_NOT_EXISTS);
        }
    }

    @Override
    public ShopDeskCategoryDO getShopDeskCategory(Long id) {
        return shopDeskCategoryMapper.selectById(id);
    }

    @Override
    public PageResult<ShopDeskCategoryDO> getShopDeskCategoryPage(ShopDeskCategoryPageReqVO pageReqVO) {
        return shopDeskCategoryMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ShopDeskCategoryDO> getShopDeskCategoryList(Long shopId) {
        return shopDeskCategoryMapper.selectList(new LambdaQueryWrapperX<ShopDeskCategoryDO>()
                .eq(ShopDeskCategoryDO::getShopId, shopId)
                .orderByAsc(ShopDeskCategoryDO::getSort)
                .orderByDesc(ShopDeskCategoryDO::getId));
    }

    @Override
    public ShopDeskCategoryDO findSuitableDeskCategory(Long shopId, Integer peopleCount) {
        if (shopId == null || peopleCount == null || peopleCount <= 0) {
            return null;
        }

        // 获取所有可用的桌型，按人数升序排序
        List<ShopDeskCategoryDO> categories = shopDeskCategoryMapper.selectList(new LambdaQueryWrapperX<ShopDeskCategoryDO>()
                .eq(ShopDeskCategoryDO::getShopId, shopId)
                .ge(ShopDeskCategoryDO::getPeople, peopleCount) // 只选择容纳人数大于等于当前人数的桌型
                .orderByAsc(ShopDeskCategoryDO::getPeople) // 按人数升序排序，优先选择最接近的
                .orderByAsc(ShopDeskCategoryDO::getId)); // 人数相同时按ID排序

        // 返回第一个合适的桌型（由于已经按人数升序排序，第一个就是最合适的）
        return categories.isEmpty() ? null : categories.get(0);
    }

    /**
     * 获取门店
     * @param id
     * @return
     */
    private StoreShopDO getShop(Long id) {
        //查找门店
        StoreShopDO storeShopDO = shopMapper.selectById(id);
        return storeShopDO;
    }

}
