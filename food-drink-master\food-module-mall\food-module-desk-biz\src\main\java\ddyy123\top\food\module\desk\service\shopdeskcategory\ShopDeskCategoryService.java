package ddyy123.top.food.module.desk.service.shopdeskcategory;

import jakarta.validation.*;
import ddyy123.top.food.module.desk.controller.admin.shopdeskcategory.vo.*;
import ddyy123.top.food.module.desk.dal.dataobject.shopdeskcategory.ShopDeskCategoryDO;
import ddyy123.top.food.framework.common.pojo.PageResult;

import java.util.List;

/**
 * 门店桌号分类 Service 接口
 *
 */
public interface ShopDeskCategoryService {

    /**
     * 创建门店桌号分类
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createShopDeskCategory(@Valid ShopDeskCategorySaveReqVO createReqVO);

    /**
     * 更新门店桌号分类
     *
     * @param updateReqVO 更新信息
     */
    void updateShopDeskCategory(@Valid ShopDeskCategorySaveReqVO updateReqVO);

    /**
     * 删除门店桌号分类
     *
     * @param id 编号
     */
    void deleteShopDeskCategory(Long id);

    /**
     * 获得门店桌号分类
     *
     * @param id 编号
     * @return 门店桌号分类
     */
    ShopDeskCategoryDO getShopDeskCategory(Long id);

    /**
     * 获得门店桌号分类分页
     *
     * @param pageReqVO 分页查询
     * @return 门店桌号分类分页
     */
    PageResult<ShopDeskCategoryDO> getShopDeskCategoryPage(ShopDeskCategoryPageReqVO pageReqVO);

    /**
     * 获取门店桌号分类列表
     *
     * @param shopId 门店ID
     * @return 桌号分类列表
     */
    List<ShopDeskCategoryDO> getShopDeskCategoryList(Long shopId);

    /**
     * 根据人数获取最合适的桌型
     *
     * @param shopId 门店ID
     * @param peopleCount 人数
     * @return 最合适的桌型，如果没有合适的返回null
     */
    ShopDeskCategoryDO findSuitableDeskCategory(Long shopId, Integer peopleCount);

}
